import { useNavigate } from 'react-router-dom'
import FeatureCard from '../components/FeatureCard'
import VersionBadge from '../components/VersionBadge'
import { ROUTES } from '../router'
import logo from '@renderer/assets/logo.png'

function HomePage() {
  const navigate = useNavigate()

  const features = [
    {
      icon: '🔇',
      title: '视频静音',
      description: '快速移除视频中的音频轨道，保留纯净的视频画面',
      route: ROUTES.VIDEO_MUTE
    },
    {
      icon: '🎵',
      title: '视频转音频',
      description: '提取视频中的音频内容，支持多种音频格式输出',
      route: ROUTES.VIDEO_TO_AUDIO
    },
    {
      icon: '🎼',
      title: '更换背景音乐',
      description: '替换视频原有音轨，添加您喜欢的背景音乐',
      route: ROUTES.REPLACE_AUDIO
    },
    {
      icon: '📦',
      title: '视频压缩',
      description: '智能压缩视频文件大小，保持画质的同时减少存储空间',
      route: ROUTES.COMPRESS_VIDEO
    },
    {
      icon: '⚡',
      title: '视频调速',
      description: '调整视频播放速度，支持加速、减速和自定义倍率',
      route: ROUTES.CHANGE_SPEED
    },
    // {
    //   icon: '✂️',
    //   title: '视频剪辑',
    //   description: '专业的视频剪切工具，支持音轨可视化和精确时间控制',
    //   route: ROUTES.VIDEO_EDIT
    // }
  ]

  const handleFeatureClick = (route: string) => {
    // 直接跳转到功能页面，文件选择在功能页面内完成
    navigate(route)
  }

  return (
    <div className="flex flex-col h-full">
      {/* App Header */}
      <header className="flex items-center justify-between px-6 py-4" style={{ backgroundColor: 'var(--bg-overlay)', backdropFilter: 'blur(8px)' }}>
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-xl flex items-center justify-center">
            <img src={logo} alt="" />
          </div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>视频剪辑</h1>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(ROUTES.SETTINGS)}
            className="transition-colors"
            style={{ color: 'var(--text-secondary)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-6xl mx-auto">
          {/* Welcome Section */}
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              专业的视频处理工具
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              简单易用的桌面应用，为您提供全面的视频编辑功能，让视频处理变得轻松高效
            </p>
          </div>

          {/* Feature Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                onClick={() => handleFeatureClick(feature.route)}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="p-4 text-center text-white/50 text-sm flex items-center justify-center space-x-2">
        <span>视频剪辑</span>
        {/* <VersionBadge /> */}
        <span>- 让视频处理更简单</span>
      </footer>
    </div>
  )
}

export default HomePage